
import os
import uuid
import json
import cv2
import face_recognition
import tempfile
from datetime import datetime
from flask import Flask, request, render_template_string, jsonify
from facial_recognition_system.face_processing import process_person_images
from facial_recognition_system.database import add_person_to_database
from gui.utils.qr_code import is_enrollment_server_running

app = Flask(__name__)
UPLOAD_BASE_FOLDER = 'student_uploads'
os.makedirs(UPLOAD_BASE_FOLDER, exist_ok=True)

# Set maximum content length
app.config['MAX_CONTENT_LENGTH'] = 64 * 1024 * 1024  # 64 MB


# Route to check if an image contains a face
@app.route('/check-face', methods=['POST'])
def check_face():
    if 'image' not in request.files:
        return jsonify({'success': False, 'message': 'No image file provided', 'face_count': 0}), 400

    file = request.files['image']
    if file.filename == '':
        return jsonify({'success': False, 'message': 'No image selected', 'face_count': 0}), 400

    if not allowed_file(file.filename):
        return jsonify({'success': False, 'message': 'File type not allowed', 'face_count': 0}), 400

    # Save the file to a temporary location
    with tempfile.NamedTemporaryFile(delete=False, suffix='.jpg') as temp:
        file.save(temp.name)
        temp_path = temp.name

    try:
        # Check for faces in the image
        success, message, face_locations = check_face_in_image(temp_path)
        face_count = len(face_locations)

        # Return the result with face count
        return jsonify({
            'success': success,
            'message': message,
            'face_count': face_count,
            'image_id': str(uuid.uuid4())  # Generate a unique ID for this image
        })
    finally:
        # Clean up the temporary file
        if os.path.exists(temp_path):
            os.remove(temp_path)

# Allowed file types
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg'}

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def check_face_in_image(file_path):
    try:
        # Load the image
        image = cv2.imread(file_path)
        if image is None:
            return False, "Could not read image file", []

        # Resize image to speed up face detection
        # Calculate new dimensions while maintaining aspect ratio
        height, width = image.shape[:2]
        max_dimension = 640  # Limit the maximum dimension for faster processing

        if max(height, width) > max_dimension:
            scale = max_dimension / max(height, width)
            new_width = int(width * scale)
            new_height = int(height * scale)
            image = cv2.resize(image, (new_width, new_height))

        # Convert to RGB (face_recognition uses RGB)
        rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)

        # Detect faces with optimized parameters
        # Use a lower resolution for faster detection
        face_locations = face_recognition.face_locations(
            rgb_image,
            model='hog',  # HOG is faster than CNN
            number_of_times_to_upsample=1  # Lower value = faster but might miss smaller faces
        )

        # Check number of faces
        if len(face_locations) == 0:
            return False, "No face", []
        elif len(face_locations) > 1:
            return False, "Multiple faces", face_locations
        else:
            return True, "One face", face_locations

    except Exception:
        return False, "Error", []


# HTML form template
HTML_FORM = '''
<!doctype html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Student Enrollment</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
    <style>
        body {
            font-family: 'Poppins', sans-serif;
            background-color: #f5f7fa;
            margin: 0;
            padding: 10px;
            color: #333;
        }
        .container {
            max-width: 800px;
            margin: 0 auto;
            background: white;
            border-radius: 10px;
            box-shadow: 0 4px 20px rgba(0,0,0,0.1);
            padding: 20px;
            position: relative;
        }
        h1, h2 {
            color: #2c3e50;
            text-align: center;
            margin-bottom: 30px;
        }
        .form-group {
            margin-bottom: 15px;
        }
        label {
            font-weight: 500;
            display: block;
            margin-bottom: 8px;
            color: #34495e;
        }
        .form-control {
            border-radius: 5px;
            border: 1px solid #ddd;
            padding: 12px 15px;
            width: 100%;
            font-size: 16px;
            transition: border-color 0.3s;
        }
        .form-control:focus {
            border-color: #3498db;
            box-shadow: 0 0 0 3px rgba(52, 152, 219, 0.2);
        }
        .btn {
            background-color: #3498db;
            color: white;
            border: none;
            padding: 12px 20px;
            border-radius: 5px;
            cursor: pointer;
            font-size: 16px;
            font-weight: 500;
            width: 100%;
            transition: background-color 0.3s;
            display: inline-flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }
        .btn:hover {
            background-color: #2980b9;
        }
        .btn:disabled {
            background-color: #95a5a6;
            cursor: not-allowed;
        }
        .file-upload {
            border: 2px dashed #ddd;
            border-radius: 5px;
            padding: 20px;
            text-align: center;
            margin-bottom: 20px;
            transition: all 0.3s;
            position: relative;
        }
        .file-upload.highlight {
            border-color: #3498db;
            background-color: rgba(52, 152, 219, 0.05);
        }
        .file-upload-label {
            display: block;
            font-weight: 500;
            margin-bottom: 10px;
            color: #34495e;
        }
        .file-upload-input {
            position: absolute;
            left: 0;
            top: 0;
            right: 0;
            bottom: 0;
            opacity: 0;
            cursor: pointer;
            width: 100%;
            height: 100%;
        }
        .file-upload-text {
            color: #7f8c8d;
            margin-bottom: 10px;
        }
        .file-upload-icon {
            font-size: 40px;
            color: #bdc3c7;
            margin-bottom: 10px;
        }
        .preview-container {
            display: flex;
            flex-wrap: wrap;
            gap: 10px;
            margin-top: 20px;
        }
        .preview-item {
            position: relative;
            width: 100px;
            height: 100px;
            border-radius: 5px;
            overflow: hidden;
            box-shadow: 0 2px 5px rgba(0,0,0,0.1);
            transition: background-color 0.3s ease;
        }
        .preview-item img {
            width: 100%;
            height: 100%;
            object-fit: cover;
        }
        .preview-remove {
            position: absolute;
            top: 5px;
            right: 5px;
            background: rgba(0,0,0,0.5);
            color: white;
            border: none;
            border-radius: 50%;
            width: 20px;
            height: 20px;
            font-size: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            cursor: pointer;
        }
        .progress-container {
            margin-top: 20px;
            margin-bottom: 20px;
            display: none;
        }

        .face-check-overlay {
            position: absolute;
            top: 5px;
            right: 30px;
            width: 24px;
            height: 24px;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            font-size: 12px;
            z-index: 5;
        }
        .face-check-overlay.success {
            background-color: #2ecc71;
        }
        .face-check-overlay.error {
            background-color: #e74c3c;
        }
        .progress {
            height: 10px;
            border-radius: 5px;
            background-color: #ecf0f1;
            overflow: hidden;
            margin-bottom: 10px;
        }
        .progress-bar {
            height: 100%;
            background-color: #2ecc71;
            width: 0%;
            transition: width 0.3s;
        }
        .progress-text {
            text-align: center;
            font-size: 14px;
            color: #7f8c8d;
        }
        .image-progress-container {
            position: absolute;
            bottom: 0;
            left: 0;
            right: 0;
            height: 5px;
            background-color: rgba(236, 240, 241, 0.8);
            overflow: hidden;
            border-bottom-left-radius: 5px;
            border-bottom-right-radius: 5px;
        }
        .image-progress-bar {
            height: 100%;
            background-color: #3498db;
            width: 0%;
            transition: width 0.2s, opacity 0.3s;
        }
        .face-error-tooltip {
            position: absolute;
            bottom: -25px;
            left: 0;
            right: 0;
            background-color: rgba(231, 76, 60, 0.9);
            color: white;
            font-size: 12px;
            padding: 3px 6px;
            border-radius: 3px;
            text-align: center;
            z-index: 10;
            pointer-events: none;
        }
        .status-badge {
            display: inline-block;
            padding: 5px 10px;
            border-radius: 20px;
            font-size: 12px;
            font-weight: 600;
            background-color: #e8f5e9;
            color: #2e7d32;
            margin-bottom: 20px;
        }
        .status-badge i {
            margin-right: 5px;
        }
        .required-field::after {
            content: "*";
            color: #e74c3c;
            margin-left: 4px;
        }
        .error-message {
            color: #e74c3c;
            font-size: 14px;
            margin-top: 5px;
            display: none;
        }
        .success-message {
            background-color: #e8f5e9;
            color: #2e7d32;
            padding: 15px;
            border-radius: 5px;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }
        .success-message i {
            font-size: 20px;
        }
        @media (max-width: 768px) {
            .container {
                padding: 20px;
            }
            .preview-item {
                width: 80px;
                height: 80px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="status-badge">
            <i class="fas fa-wifi"></i> Connected
        </div>
        <h2>Student Upload Form</h2>
        <form method="POST" enctype="multipart/form-data" id="uploadForm">
            <div class="form-group">
                <label for="name" class="required-field">nom</label>
                <input type="text" class="form-control" id="name" name="name" required>
                <div class="error-message" id="nameError">saisir le nom s'il vous plaît</div>
            </div>

            <div class="form-group">
                <label for="familyName" class="required-field">prenom</label>
                <input type="text" class="form-control" id="familyName" name="family_name" required>
                <div class="error-message" id="familyNameError">saisir le prenom s'il vous plaît</div>
            </div>

            <div class="form-group">
                <label for="dob" class="required-field">date de naissance</label>
                <input type="date" class="form-control" id="dob" name="dob" required>
                <div class="error-message" id="dobError"></div>
            </div>

            <div class="form-group">
                <label class="required-field">photo des visages</label>
                <div class="file-upload" id="dropArea">
                    <i class="fas fa-cloud-upload-alt file-upload-icon"></i>
                    <p class="file-upload-text">Glissez-déposez vos images ici ou cliquez pour parcourir</p>
                    <p class="file-upload-text">Veuillez télécharger au moins une photo claire de votre visage (maximum 10)</p>
                    <input type="file" class="file-upload-input" id="fileInput" name="files[]" accept="image/*" multiple>
                </div>
                <div class="error-message" id="fileError">Veuillez télécharger au moins une image</div>

                <div class="preview-container" id="previewContainer"></div>

                <div class="progress-container" id="progressContainer">
                    <div class="progress">
                        <div class="progress-bar" id="progressBar"></div>
                    </div>
                    <div class="progress-text" id="progressText">Uploading... 0%</div>
                </div>
            </div>

            <button type="submit" class="btn" id="submitBtn",>
                <i class="fas fa-paper-plane"></i>soumettre
            </button>
        </form>
    </div>

    <script>
        // File upload preview functionality
        const dropArea = document.getElementById('dropArea');
        const fileInput = document.getElementById('fileInput');
        const previewContainer = document.getElementById('previewContainer');
        const progressContainer = document.getElementById('progressContainer');
        const progressBar = document.getElementById('progressBar');
        const progressText = document.getElementById('progressText');
        const submitBtn = document.getElementById('submitBtn');
        const form = document.getElementById('uploadForm');

        // Prevent default drag behaviors
        ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
            dropArea.addEventListener(eventName, preventDefaults, false);
            document.body.addEventListener(eventName, preventDefaults, false);
        });

        // Highlight drop area when item is dragged over it
        ['dragenter', 'dragover'].forEach(eventName => {
            dropArea.addEventListener(eventName, highlight, false);
        });

        ['dragleave', 'drop'].forEach(eventName => {
            dropArea.addEventListener(eventName, unhighlight, false);
        });

        // Handle dropped files
        dropArea.addEventListener('drop', handleDrop, false);

        // Handle selected files
        fileInput.addEventListener('change', handleFiles, false);

        function preventDefaults(e) {
            e.preventDefault();
            e.stopPropagation();
        }

        function highlight() {
            dropArea.classList.add('highlight');
        }

        function unhighlight() {
            dropArea.classList.remove('highlight');
        }

        function handleDrop(e) {
            const dt = e.dataTransfer;
            const files = dt.files;
            handleFiles({ target: { files } });
        }

        async function handleFiles(e) {
            const files = e.target.files;

            // Check if adding these files would exceed the 10-image limit
            const currentImageCount = previewContainer.children.length;
            if (currentImageCount + files.length > 10) {
                alert(`You already have ${currentImageCount} images. You can only add ${10 - currentImageCount} more (maximum 10 total).`);
                return;
            }

            // Don't clear preview - keep existing images
            // Process each file
            for (const file of [...files]) {
                if (!file.type.match('image.*')) {
                    alert('Please upload only image files.');
                    continue;
                }

                const reader = new FileReader();
                reader.onload = async function(e) {
                    const preview = document.createElement('div');
                    preview.className = 'preview-item';

                    const img = document.createElement('img');
                    img.src = e.target.result;
                    // No need for client-side face detection

                    const removeBtn = document.createElement('button');
                    removeBtn.className = 'preview-remove';
                    removeBtn.innerHTML = '<i class="fas fa-times"></i>';
                    removeBtn.addEventListener('click', function() {
                        preview.remove();

                        // Update error message if no images
                        document.getElementById('fileError').style.display =
                            previewContainer.querySelectorAll('.preview-item').length === 0 ? 'block' : 'none';
                    });

                    preview.appendChild(img);
                    preview.appendChild(removeBtn);
                    previewContainer.appendChild(preview);
                }
                reader.readAsDataURL(file);
            }

            // Show error if no files
            document.getElementById('fileError').style.display = files.length === 0 ? 'block' : 'none';
        }

        // Function to check faces in images
        async function checkFaces(previewItems) {
            // Process each image
            let validCount = 0;
            let hasErrors = false;

            // Add progress bars to all images first
            for (let i = 0; i < previewItems.length; i++) {
                // Remove any existing progress bars
                const existingProgress = previewItems[i].querySelector('.image-progress-container');
                if (existingProgress) {
                    previewItems[i].removeChild(existingProgress);
                }

                // Create and add progress bar
                const progressContainer = document.createElement('div');
                progressContainer.className = 'image-progress-container';

                const progressBar = document.createElement('div');
                progressBar.className = 'image-progress-bar';
                progressBar.style.width = '0%';

                progressContainer.appendChild(progressBar);
                previewItems[i].appendChild(progressContainer);

                // Reset the background color
                previewItems[i].style.backgroundColor = '';

                // Remove any existing overlay
                const existingOverlay = previewItems[i].querySelector('.face-check-overlay');
                if (existingOverlay) {
                    previewItems[i].removeChild(existingOverlay);
                }

                // Remove any existing tooltip
                const existingTooltip = previewItems[i].querySelector('.face-error-tooltip');
                if (existingTooltip) {
                    previewItems[i].removeChild(existingTooltip);
                }
            }

            for (let i = 0; i < previewItems.length; i++) {
                const img = previewItems[i].querySelector('img');
                const imgSrc = img.src;
                const progressBar = previewItems[i].querySelector('.image-progress-bar');

                // Update progress to show we're starting
                progressBar.style.width = '10%';

                try {
                    // Convert data URL to Blob
                    progressBar.style.width = '20%';
                    const blob = await fetch(imgSrc).then(r => r.blob());

                    // Create a FormData object
                    progressBar.style.width = '30%';
                    const formData = new FormData();
                    formData.append('image', blob, 'image.jpg');

                    // Update progress to show we're sending to server
                    progressBar.style.width = '50%';

                    // Send to server for face detection
                    const response = await fetch('/check-face', {
                        method: 'POST',
                        body: formData
                    });

                    // Update progress to show we're processing the response
                    progressBar.style.width = '80%';

                    const result = await response.json();

                    // Complete the progress bar
                    progressBar.style.width = '100%';

                    // After a short delay, fade out the progress bar
                    setTimeout(() => {
                        progressBar.style.opacity = '0';
                        setTimeout(() => {
                            const progressContainer = previewItems[i].querySelector('.image-progress-container');
                            if (progressContainer) {
                                previewItems[i].removeChild(progressContainer);
                            }
                        }, 300);
                    }, 200);

                    if (result.success) {
                        validCount++;
                        // Add a green border to the image
                        previewItems[i].style.backgroundColor = 'rgba(46, 204, 113, 0.3)';

                        // Store the image ID in the preview item
                        previewItems[i].setAttribute('data-image-id', result.image_id);

                        // Add a success indicator to the image
                        const overlay = document.createElement('div');
                        overlay.className = 'face-check-overlay success';
                        overlay.innerHTML = '<i class="fas fa-check"></i>';
                        previewItems[i].appendChild(overlay);
                    } else {
                        hasErrors = true;
                        // Add a red background to the image
                        previewItems[i].style.backgroundColor = 'rgba(231, 76, 60, 0.3)';

                        // Add an error indicator to the image
                        const overlay = document.createElement('div');
                        overlay.className = 'face-check-overlay error';

                        // Check if multiple faces were detected
                        if (result.face_count > 1) {
                            overlay.innerHTML = '<i class="fas fa-users"></i>';
                            overlay.title = `Multiple faces detected (${result.face_count}). Please upload an image with only one face.`;

                            // Add a tooltip element with the error message
                            const tooltip = document.createElement('div');
                            tooltip.className = 'face-error-tooltip';
                            tooltip.textContent = `Multiple faces (${result.face_count})`;
                            previewItems[i].appendChild(tooltip);
                        } else {
                            overlay.innerHTML = '<i class="fas fa-times"></i>';
                        }

                        previewItems[i].appendChild(overlay);
                    }
                } catch (error) {
                    console.error('Error checking face:', error);
                    hasErrors = true;

                    // Complete the progress bar
                    progressBar.style.width = '100%';
                    progressBar.style.backgroundColor = '#e74c3c';

                    // After a short delay, fade out the progress bar
                    setTimeout(() => {
                        progressBar.style.opacity = '0';
                        setTimeout(() => {
                            const progressContainer = previewItems[i].querySelector('.image-progress-container');
                            if (progressContainer) {
                                previewItems[i].removeChild(progressContainer);
                            }
                        }, 300);
                    }, 200);

                    // Add a red background to the image
                    previewItems[i].style.backgroundColor = 'rgba(231, 76, 60, 0.3)';

                    // Add an error indicator to the image
                    const overlay = document.createElement('div');
                    overlay.className = 'face-check-overlay error';
                    overlay.innerHTML = '<i class="fas fa-exclamation-triangle"></i>';
                    previewItems[i].appendChild(overlay);
                }
            }

            return { validCount, hasErrors };
        }

        // Form validation
        form.addEventListener('submit', async function(e) {
            // Always prevent default to handle submission manually
            e.preventDefault();

            let isValid = true;

            // Validate name
            const name = document.getElementById('name').value.trim();
            if (name === '') {
                document.getElementById('nameError').style.display = 'block';
                isValid = false;
            } else {
                document.getElementById('nameError').style.display = 'none';
            }

            // Validate family name
            const familyName = document.getElementById('familyName').value.trim();
            if (familyName === '') {
                document.getElementById('familyNameError').style.display = 'block';
                isValid = false;
            } else {
                document.getElementById('familyNameError').style.display = 'none';
            }

            // Validate date of birth
            const dob = document.getElementById('dob').value;
            if (dob === '') {
                document.getElementById('dobError').style.display = 'block';
                isValid = false;
            } else {
                document.getElementById('dobError').style.display = 'none';
            }

            // Validate files - make sure at least one image is selected or already in preview
            const previewItems = previewContainer.querySelectorAll('.preview-item');
            if (previewItems.length === 0) {
                document.getElementById('fileError').style.display = 'block';
                isValid = false;
            } else {
                document.getElementById('fileError').style.display = 'none';
            }

            if (!isValid) {
                return;
            }

            // Show progress
            progressContainer.style.display = 'block';
            submitBtn.disabled = true;
            progressText.textContent = 'Checking faces...';
            progressBar.style.width = '20%';

            // Check faces in all images

            const { validCount, hasErrors } = await checkFaces(previewItems);

            // Check if there are any face errors
            const multipleErrorItems = document.querySelectorAll('.face-error-tooltip');
            if (multipleErrorItems.length > 0) {
                submitBtn.disabled = false;
                progressContainer.style.display = 'none';
                alert('Please remove images with multiple faces before submitting.');
                return;
            }

            if (hasErrors) {
                submitBtn.disabled = false;
                progressContainer.style.display = 'none';
                alert('Some images have face detection errors. Please fix or remove them before submitting.');
                return;
            }

            if (validCount === 0) {
                submitBtn.disabled = false;
                progressContainer.style.display = 'none';
                alert('No valid faces detected in any of the images. Please upload at least one clear photo of your face.');
                return;
            }

            // Update progress
            progressText.textContent = 'Preparing to upload...';
            progressBar.style.width = '10%';

            // Create FormData object
            const formData = new FormData(form);

            // Add face check results to form data
            const faceCheckResults = [];
            for (let i = 0; i < previewItems.length; i++) {
                const overlay = previewItems[i].querySelector('.face-check-overlay');
                if (overlay && overlay.className.includes('success')) {
                    // Get the image ID from the data attribute
                    const imageId = previewItems[i].getAttribute('data-image-id');
                    if (imageId) {
                        faceCheckResults.push(imageId);
                    }
                }
            }

            // Add the face check results to the form data
            formData.append('face_check_results', JSON.stringify(faceCheckResults));

            // Use XMLHttpRequest for progress tracking
            const xhr = new XMLHttpRequest();

            // Track upload progress
            xhr.upload.addEventListener('progress', function(e) {
                if (e.lengthComputable) {
                    const percentComplete = Math.round((e.loaded / e.total) * 90) + 10; // Start at 10%, go to 100%
                    progressBar.style.width = percentComplete + '%';
                    progressText.textContent = `Uploading... ${percentComplete}%`;
                }
            });

            // Handle completion
            xhr.addEventListener('load', function() {
                if (xhr.status >= 200 && xhr.status < 300) {
                    progressBar.style.width = '100%';
                    progressText.textContent = 'Complete!';

                    // Replace the current page with the response
                    document.open();
                    document.write(xhr.responseText);
                    document.close();
                } else {
                    // Handle error
                    submitBtn.disabled = false;
                    progressContainer.style.display = 'none';
                    alert('Upload failed. Please try again.');
                }
            });

            // Handle errors
            xhr.addEventListener('error', function() {
                submitBtn.disabled = false;
                progressContainer.style.display = 'none';
                alert('Upload failed. Please check your connection and try again.');
            });

            // Open and send the request
            xhr.open('POST', form.action || window.location.href);
            xhr.send(formData);
        });
    </script>
</body>
</html>
'''

@app.route('/', methods=['GET', 'POST'])
def upload_file():
    if request.method == 'POST':
        # Check if enrollment is still allowed
        if not is_enrollment_server_running():
            return get_enrollment_closed_html(), 403

        try:
            # Get student info
            name = request.form.get('name', '').strip()
            family_name = request.form.get('family_name', '').strip()
            dob = request.form.get('dob', '').strip()
            class_id = request.form.get('class_id', '').strip()

            # Get class name from form data or query parameters
            class_name = request.args.get('class_name', 'Student Enrollment')
            if class_id and class_name == 'Student Enrollment':
                class_name = request.form.get('class_name', class_name)

            # Validate student info
            if not all([name, family_name, dob]):
                return "Missing student information", 400

            # Create unique folder structure
            student_id = f"{uuid.uuid4()}_{int(datetime.now().timestamp())}"

            # Create class folder if class_id is provided
            if class_id:
                class_folder = os.path.join(UPLOAD_BASE_FOLDER, f"class_{class_id}")
                os.makedirs(class_folder, exist_ok=True)
                student_folder = os.path.join(class_folder, student_id)
            else:
                # Fallback to the original structure if no class_id
                student_folder = os.path.join(UPLOAD_BASE_FOLDER, student_id)

            os.makedirs(student_folder)

            # Save student info
            with open(os.path.join(student_folder, "info.txt"), 'w') as f:
                f.write(f"Name: {name}\n")
                f.write(f"Family Name: {family_name}\n")
                f.write(f"Date of Birth: {dob}\n")
                f.write(f"Class ID: {class_id}\n")
                f.write(f"Class Name: {class_name}\n")
                f.write(f"Upload Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")

            # Handle image uploads
            if 'files[]' not in request.files:
                return "No files uploaded", 400

            files = request.files.getlist('files[]')
            uploaded = []
            face_errors = []

            # Get the face check results from the client
            face_check_results = []
            try:
                face_check_results_json = request.form.get('face_check_results', '[]')
                face_check_results = json.loads(face_check_results_json)
            except Exception:
                pass

            # Create a dictionary to track which files have been processed
            processed_files = {}

            for file in files:
                if file.filename == '':
                    continue
                if not allowed_file(file.filename):
                    continue

                # Save the file to a temporary location
                with tempfile.NamedTemporaryFile(delete=False, suffix='.jpg') as temp:
                    file.save(temp.name)
                    temp_path = temp.name

                try:
                    # Generate a unique filename with timestamp to avoid conflicts
                    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
                    unique_filename = f"{timestamp}_{file.filename}"
                    file_path = os.path.join(student_folder, unique_filename)

                    # Copy the file from temp location to the student folder
                    os.rename(temp_path, file_path)
                    uploaded.append(unique_filename)
                    processed_files[file.filename] = unique_filename
                except Exception as e:
                    face_errors.append(f"Image '{file.filename}': {"Erreur lors de la sauvegarde de l'image"}")

                    # Clean up the temporary file
                    if os.path.exists(temp_path):
                        os.remove(temp_path)

            if not uploaded:
                # If there were no valid uploads
                return "Aucune image de visage valide n'a été téléchargée. Veuillez réessayer avec des photos claires de visages.", 400


            # If some images had face errors but at least one was valid
            if face_errors and uploaded:
                pass

            # Process the images to create face encodings
            full_name = f"{name} {family_name}"
            encodings = process_person_images(student_folder)

            # Add the student to the database with the class ID
            database_success = False
            database_error = None

            if encodings and len(encodings) > 0:
                try:
                    success = add_person_to_database(full_name, encodings, class_id if class_id else None)
                    if success:
                        database_success = True
                    else:
                        database_error = "Failed to add student to database"
                except Exception as e:
                    error_message = str(e)

                    # Check for specific error types
                    if "UNIQUE constraint failed" in error_message:
                        database_error = "A student with this name already exists in the database"
                    else:
                        database_error = f"Database error: {error_message}"
            else:
                database_error = "No valid face images were detected"

            # Return success page using the helper function
            return get_enrollment_success_html(student_id, class_id, class_name, uploaded, database_success, database_error)

        except Exception as e:
            return f"An unexpected error occurred: {str(e)}", 500

    # Handle GET requests
    try:
        # Check if enrollment is still allowed for GET requests
        if not is_enrollment_server_running():
            return get_enrollment_closed_html(), 403

        # Get class info from query parameters
        class_id = request.args.get('class_id', '')
        class_name = request.args.get('class_name', 'Student Enrollment')

        # Check if this is just a status check
        if request.args.get('check') == '1':
            return "", 200


        # Customize the HTML form with class info
        custom_form = HTML_FORM.replace('<h2>Student Upload Form</h2>',
                                      f'<h2>Inscription pour {class_name}</h2>')

        # Add hidden fields for class_id and class_name
        custom_form = custom_form.replace('<form method="POST" enctype="multipart/form-data" id="uploadForm">',
                                        f'<form method="POST" enctype="multipart/form-data" id="uploadForm">\n'
                                        f'<input type="hidden" name="class_id" value="{class_id}">\n'
                                        f'<input type="hidden" name="class_name" value="{class_name}">')

        # Add JavaScript to periodically check if enrollment is still open
        check_enrollment_script = '''
        <script>
            // Check enrollment status every 5 seconds
            setInterval(function() {
                fetch(window.location.href + '?check=1')
                    .then(response => {
                        if (response.status === 403) {
                            // Enrollment closed, reload the page to show the closed message
                            window.location.reload();
                        }
                    })
                    .catch(error => {
                        console.error('Error checking enrollment status:', error);
                    });
            }, 5000);
        </script>
        '''

        # Add the script before the closing body tag
        custom_form = custom_form.replace('</body>', f'{check_enrollment_script}\n</body>')

        return render_template_string(custom_form)

    except Exception as e:
        return f"An error occurred while loading the enrollment form: {str(e)}", 500

def get_enrollment_closed_html():
    return '''
    <!doctype html>
    <html>
    <head>
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title>Session Ended</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Inter:wght@300;400;500;600&display=swap" rel="stylesheet">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
        <style>
            body {
                font-family: 'Inter', sans-serif;
                background-color: #f8fafc;
                margin: 0;
                padding: 0;
                color: #1e293b;
                display: flex;
                justify-content: center;
                align-items: center;
                min-height: 100vh;
            }
            .closed-container {
                background-color: white;
                border-radius: 12px;
                box-shadow: 0 4px 20px rgba(0, 0, 0, 0.08);
                padding: 40px;
                max-width: 400px;
                width: 100%;
                text-align: center;
            }
            .icon-container {
                margin-bottom: 24px;
            }
            .animated-icon {
                font-size: 64px;
                color: #ef4444;
                animation: pulse 2s infinite;
            }
            @keyframes pulse {
                0% { transform: scale(1); }
                50% { transform: scale(1.05); }
                100% { transform: scale(1); }
            }
            h1 {
                font-size: 24px;
                font-weight: 600;
                margin-bottom: 16px;
                color: #0f172a;
            }
            p {
                font-size: 16px;
                line-height: 1.5;
                color: #64748b;
                margin-bottom: 0;
            }
        </style>
    </head>
    <body>
        <div class="closed-container">
            <div class="icon-container">
                <i class="fas fa-lock animated-icon"></i>
            </div>
            <h1>Session Ended</h1>
            <p>This enrollment session is no longer available.</p>
        </div>
    </body>
    </html>
    '''

def get_enrollment_success_html(student_id, class_id, class_name, uploaded, database_success, database_error):
    return f'''
    <!doctype html>
    <html>
    <head>
        <meta name="viewport" content="width=device-width, initial-scale=1">
        <title>Enrollment Complete</title>
        <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
        <link href="https://fonts.googleapis.com/css2?family=Poppins:wght@300;400;500;600;700&display=swap" rel="stylesheet">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">
        <style>
            body {{
                font-family: 'Poppins', sans-serif;
                background-color: #f5f7fa;
                margin: 0;
                padding: 20px;
                color: #333;
                display: flex;
                justify-content: center;
                align-items: center;
                min-height: 100vh;
            }}
            .success-card {{
                max-width: 600px;
                background: white;
                border-radius: 10px;
                box-shadow: 0 4px 20px rgba(0,0,0,0.1);
                padding: 40px 30px;
                text-align: center;
            }}
            h1 {{
                color: #2ecc71;
                margin-bottom: 20px;
                font-weight: 600;
            }}
            p {{
                color: #555;
                font-size: 16px;
                line-height: 1.6;
                margin-bottom: 15px;
            }}
            .success-icon {{
                width: 100px;
                height: 100px;
                background-color: #e8f5e9;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                margin: 0 auto 25px;
            }}
            .success-icon i {{
                font-size: 50px;
                color: #2ecc71;
            }}
            .animated-checkmark {{
                animation: checkmark 0.8s ease-in-out forwards;
            }}
            @keyframes checkmark {{
                0% {{ transform: scale(0); opacity: 0; }}
                50% {{ transform: scale(1.2); opacity: 1; }}
                100% {{ transform: scale(1); opacity: 1; }}
            }}
            .info-item {{
                background-color: #f8f9fa;
                border-radius: 8px;
                padding: 15px;
                margin-bottom: 15px;
                text-align: left;
                border-left: 4px solid #3498db;
            }}
            .info-label {{
                font-weight: 600;
                color: #3498db;
                margin-bottom: 5px;
                font-size: 14px;
            }}
            .info-value {{
                font-size: 16px;
                color: #333;
                word-break: break-all;
            }}
            .upload-count {{
                display: inline-block;
                padding: 8px 16px;
                border-radius: 20px;
                font-size: 14px;
                font-weight: 500;
                background-color: #e8f5e9;
                color: #2e7d32;
                margin: 20px 0;
            }}
            .upload-count i {{
                margin-right: 5px;
            }}
            .btn {{
                background-color: #3498db;
                color: white;
                border: none;
                padding: 12px 25px;
                border-radius: 5px;
                cursor: pointer;
                font-size: 16px;
                font-weight: 500;
                display: inline-flex;
                align-items: center;
                gap: 8px;
                text-decoration: none;
                transition: background-color 0.3s;
            }}
            .btn:hover {{
                background-color: #2980b9;
                color: white;
                text-decoration: none;
            }}
        </style>
    </head>
    <body>
        <div class="success-card">
            <div class="success-icon" style="{'' if database_success else 'background-color: #fff3e0;'}">
                <i class="fas {'fa-check animated-checkmark' if database_success else 'fa-exclamation-triangle'}" style="{'' if database_success else 'color: #ff9800;'}"></i>
            </div>
            <h1 style="{'' if database_success else 'color: #ff9800;'}">{'Enrollment Complete!' if database_success else 'Enrollment Completed with Issues'}</h1>
            <p>Your information has been successfully submitted{' and added to the database' if database_success else ''}.</p>
            {f'''
            <div class="info-item" style="background-color: #fff3e0; border-left: 4px solid #ff9800;">
                <div class="info-label" style="color: #e65100;">Note</div>
                <div class="info-value" style="color: #ff9800;">{database_error}</div>
            </div>
            ''' if not database_success and database_error is not None else ''}

            <div class="info-item">
                <div class="info-label">Student ID</div>
                <div class="info-value">{student_id}</div>
            </div>

            {f'''
            <div class="info-item">
                <div class="info-label">Class</div>
                <div class="info-value">{class_name}</div>
            </div>
            ''' if class_id else ''}

            <div class="upload-count">
                <i class="fas fa-images"></i> {len(uploaded)} image{'' if len(uploaded) == 1 else 's'} uploaded
            </div>

            <div style="display: flex; gap: 10px; justify-content: center; flex-wrap: wrap;">
                {f'''
                <a href="/?class_id={class_id}&class_name={class_name}" class="btn">
                    <i class="fas fa-arrow-left"></i> Back to Class Enrollment
                </a>
                ''' if class_id else '''
                <a href="/" class="btn">
                    <i class="fas fa-arrow-left"></i> Back to Enrollment Form
                </a>
                '''}
            </div>
        </div>
    </body>
    </html>
    '''

def run_server(host=None, port=None):
    """
    Run the enrollment server.

    Args:
        host: The host to bind the server to (default from Config)
        port: The port to bind the server to (default from Config)
    """
    from facial_recognition_system.config import Config
    if host is None:
        host = Config.HOST
    if port is None:
        port = Config.ENROLLMENT_PORT

    # Make sure the enrollment server is marked as running when it starts
    if not is_enrollment_server_running():
        # Set the global variable directly to ensure it's running
        import gui.utils.qr_code
        gui.utils.qr_code._enrollment_server_running = True

    import logging
    logging.getLogger('werkzeug').setLevel(logging.WARNING)
    try:
        app.run(host=host, port=port, threaded=True)
    except Exception:
        raise


if __name__ == '__main__':
    run_server()