import os
import uuid
import cv2
import face_recognition
import tempfile
from datetime import datetime
from flask import Flask, request, render_template_string, jsonify
from facial_recognition_system.face_processing import process_person_images
from facial_recognition_system.database import add_person_to_database
from gui.utils.qr_code import is_enrollment_server_running

app = Flask(__name__)
UPLOAD_BASE_FOLDER = 'student_uploads'
ALLOWED_EXTENSIONS = {'png', 'jpg', 'jpeg'}

os.makedirs(UPLOAD_BASE_FOLDER, exist_ok=True)
app.config['MAX_CONTENT_LENGTH'] = 64 * 1024 * 1024  # 64 MB

def allowed_file(filename):
    return '.' in filename and filename.rsplit('.', 1)[1].lower() in ALLOWED_EXTENSIONS

def check_face_in_image(file_path):
    try:
        image = cv2.imread(file_path)
        if image is None:
            return False, "Could not read image", []

        # Resize if too large
        height, width = image.shape[:2]
        if max(height, width) > 640:
            scale = 640 / max(height, width)
            new_width = int(width * scale)
            new_height = int(height * scale)
            image = cv2.resize(image, (new_width, new_height))

        # Detect faces
        rgb_image = cv2.cvtColor(image, cv2.COLOR_BGR2RGB)
        face_locations = face_recognition.face_locations(rgb_image, model='hog')

        if len(face_locations) == 0:
            return False, "No face", []
        elif len(face_locations) > 1:
            return False, "Multiple faces", face_locations
        else:
            return True, "One face", face_locations
    except Exception:
        return False, "Error", []

@app.route('/check-face', methods=['POST'])
def check_face():
    if 'image' not in request.files:
        return jsonify({'success': False, 'message': 'No image file', 'face_count': 0}), 400

    file = request.files['image']
    if file.filename == '' or not allowed_file(file.filename):
        return jsonify({'success': False, 'message': 'Invalid file', 'face_count': 0}), 400

    with tempfile.NamedTemporaryFile(delete=False, suffix='.jpg') as temp:
        file.save(temp.name)
        temp_path = temp.name

    try:
        success, message, face_locations = check_face_in_image(temp_path)
        return jsonify({
            'success': success,
            'message': message,
            'face_count': len(face_locations),
            'image_id': str(uuid.uuid4())
        })
    finally:
        if os.path.exists(temp_path):
            os.remove(temp_path)

# Simple HTML form
HTML_FORM = '''
<!doctype html>
<html>
<head>
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Student Enrollment</title>
    <style>
        body { font-family: Arial, sans-serif; max-width: 600px; margin: 50px auto; padding: 20px; }
        .form-group { margin-bottom: 15px; }
        label { display: block; margin-bottom: 5px; font-weight: bold; }
        input, button { width: 100%; padding: 10px; border: 1px solid #ddd; border-radius: 4px; }
        button { background: #007bff; color: white; cursor: pointer; }
        button:hover { background: #0056b3; }
        .file-upload { border: 2px dashed #ddd; padding: 20px; text-align: center; margin: 10px 0; }
    </style>
</head>
<body>
    <h2>Student Enrollment</h2>
    <form method="POST" enctype="multipart/form-data">
        <div class="form-group">
            <label>Name:</label>
            <input type="text" name="name" required>
        </div>
        <div class="form-group">
            <label>Family Name:</label>
            <input type="text" name="family_name" required>
        </div>
        <div class="form-group">
            <label>Date of Birth:</label>
            <input type="date" name="dob" required>
        </div>
        <div class="form-group">
            <label>Photos:</label>
            <div class="file-upload">
                <input type="file" name="files[]" accept="image/*" multiple required>
                <p>Select one or more photos of your face</p>
            </div>
        </div>
        <button type="submit">Submit</button>
    </form>
</body>
</html>
'''

@app.route('/', methods=['GET', 'POST'])
def upload_file():
    if request.method == 'POST':
        # Check if enrollment is allowed
        if not is_enrollment_server_running():
            return "Enrollment session ended", 403

        # Get form data
        name = request.form.get('name', '').strip()
        family_name = request.form.get('family_name', '').strip()
        dob = request.form.get('dob', '').strip()
        class_id = request.form.get('class_id', '').strip()

        # Validate required fields
        if not all([name, family_name, dob]):
            return "Missing required information", 400

        # Create student folder
        student_id = f"{uuid.uuid4()}_{int(datetime.now().timestamp())}"
        if class_id:
            class_folder = os.path.join(UPLOAD_BASE_FOLDER, f"class_{class_id}")
            os.makedirs(class_folder, exist_ok=True)
            student_folder = os.path.join(class_folder, student_id)
        else:
            student_folder = os.path.join(UPLOAD_BASE_FOLDER, student_id)
        
        os.makedirs(student_folder)

        # Save student info
        with open(os.path.join(student_folder, "info.txt"), 'w') as f:
            f.write(f"Name: {name}\n")
            f.write(f"Family Name: {family_name}\n")
            f.write(f"Date of Birth: {dob}\n")
            f.write(f"Class ID: {class_id}\n")
            f.write(f"Upload Time: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")

        # Handle file uploads
        if 'files[]' not in request.files:
            return "No files uploaded", 400

        files = request.files.getlist('files[]')
        uploaded = []

        for file in files:
            if file.filename == '' or not allowed_file(file.filename):
                continue

            # Save file with unique name
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S_%f")
            filename = f"{timestamp}_{file.filename}"
            file_path = os.path.join(student_folder, filename)
            file.save(file_path)
            uploaded.append(filename)

        if not uploaded:
            return "No valid images uploaded", 400

        # Process face encodings
        full_name = f"{name} {family_name}"
        encodings = process_person_images(student_folder)

        # Add to database
        database_success = False
        database_error = None
        
        if encodings and len(encodings) > 0:
            try:
                success = add_person_to_database(full_name, encodings, class_id if class_id else None)
                if success:
                    database_success = True
                else:
                    database_error = "Failed to add to database"
            except Exception as e:
                if "UNIQUE constraint failed" in str(e):
                    database_error = "Student name already exists"
                else:
                    database_error = f"Database error: {str(e)}"
        else:
            database_error = "No valid face images detected"

        # Return success page
        return get_enrollment_success_html(student_id, class_id, uploaded, database_success, database_error)

    else:
        # GET request - show form
        if not is_enrollment_server_running():
            return get_enrollment_closed_html(), 403

        class_id = request.args.get('class_id', '')
        class_name = request.args.get('class_name', 'Student Enrollment')
        
        if request.args.get('check') == '1':
            return "", 200

        # Customize form
        custom_form = HTML_FORM.replace('<h2>Student Enrollment</h2>', f'<h2>Inscription pour {class_name}</h2>')
        custom_form = custom_form.replace('<form method="POST"', f'<form method="POST"')
        
        if class_id:
            custom_form = custom_form.replace('<form method="POST" enctype="multipart/form-data">',
                                            f'<form method="POST" enctype="multipart/form-data">\n'
                                            f'<input type="hidden" name="class_id" value="{class_id}">')

        return render_template_string(custom_form)

def get_enrollment_closed_html():
    return '''
    <!doctype html>
    <html>
    <head><title>Session Ended</title></head>
    <body style="font-family: Arial; text-align: center; margin-top: 100px;">
        <h1>Enrollment Session Ended</h1>
        <p>This enrollment session is no longer available.</p>
    </body>
    </html>
    '''

def get_enrollment_success_html(student_id, class_id, uploaded, database_success, database_error):
    status = "Success!" if database_success else "Completed with Issues"
    color = "#28a745" if database_success else "#ffc107"
    
    error_section = ""
    if database_error:
        error_section = f'<div style="background: #f8d7da; padding: 10px; margin: 10px 0; border-radius: 4px; color: #721c24;">{database_error}</div>'
    
    class_info = f'<p>Class: {class_id}</p>' if class_id else ''

    return f'''
    <!doctype html>
    <html>
    <head><title>Enrollment Complete</title></head>
    <body style="font-family: Arial; max-width: 600px; margin: 50px auto; padding: 20px; text-align: center;">
        <h1 style="color: {color};">{status}</h1>
        <p>Student ID: {student_id}</p>
        {class_info}
        <p>Images uploaded: {len(uploaded)}</p>
        {error_section}
        <a href="/" style="display: inline-block; padding: 10px 20px; background: #007bff; color: white; text-decoration: none; border-radius: 4px;">Back to Form</a>
    </body>
    </html>
    '''

def run_server(host=None, port=None):
    from facial_recognition_system.config import Config
    if host is None:
        host = Config.HOST
    if port is None:
        port = Config.ENROLLMENT_PORT

    import logging
    logging.getLogger('werkzeug').setLevel(logging.WARNING)
    app.run(host=host, port=port, threaded=True)

if __name__ == '__main__':
    run_server()
